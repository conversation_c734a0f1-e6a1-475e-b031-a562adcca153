import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../context/LanguageContext';
import EnhancedScholarshipCard from './EnhancedScholarshipCard';
import GreatYOPPagination from './GreatYOPPagination';
import UnifiedSidebar from './UnifiedSidebar';
import CommentsSection from './CommentsSection';
import AdPlacement from './AdPlacement';
import { Pagination, Spin, Alert } from 'antd';

interface Scholarship {
  id: number;
  title: string;
  description: string;
  level: string;
  country: string;
  deadline: string;
  isOpen: boolean;
  thumbnail: string;
  fundingSource?: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface HeroArticle {
  category: string;
  content: string[];
}

interface LevelPageConfig {
  level: string;
  title: string;
  description: string;
  keywords: string;
  heroTitle: string;
  heroSubtitle: string;
  heroArticle?: HeroArticle;
  infoTitle: string;
  infoContent: string;
  benefits: string[];
  apiEndpoint: string;
}

interface StandardLevelPageProps {
  config: LevelPageConfig;
}

const StandardLevelPage: React.FC<StandardLevelPageProps> = ({ config }) => {
  const { translations } = useLanguage();
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 6,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });

  // Handle scholarship card click
  const handleScholarshipClick = (id: number, slug?: string) => {
    if (slug) {
      window.location.href = `/bourse/${slug}`;
    } else {
      window.location.href = `/scholarships/${id}`;
    }
  };

  const fetchScholarships = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams({
        level: config.level,
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      });

      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${config.apiEndpoint}?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch scholarships');
      }

      const data = await response.json();
      console.log('API Response:', data); // Debug log

      if (data.success) {
        const scholarshipsData = data.data || [];
        setScholarships(scholarshipsData);

        // Set pagination data with fallback values
        const paginationData = {
          total: data.pagination?.total || scholarshipsData.length || 0,
          totalPages: data.pagination?.totalPages || Math.ceil((scholarshipsData.length || 0) / pagination.limit),
          hasNextPage: data.pagination?.hasNextPage || false,
          hasPreviousPage: data.pagination?.hasPreviousPage || false
        };

        console.log('Pagination Data:', paginationData); // Debug log

        setPagination(prev => ({
          ...prev,
          ...paginationData
        }));
      } else {
        throw new Error(data.message || 'Failed to load scholarships');
      }
    } catch (error) {
      console.error('Error fetching scholarships:', error);
      setError('Impossible de charger les bourses. Veuillez réessayer plus tard.');
      setScholarships([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchScholarships();
  }, [pagination.page, config.level]);

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const sidebarConfig = {
    type: 'levels' as const,
    currentItem: config.level,
    limit: 10
  };

  return (
    <>
      <Helmet>
        <title>{config.title}</title>
        <meta name="description" content={config.description} />
        <meta name="keywords" content={config.keywords} />
      </Helmet>

      <div className="min-h-screen bg-gradient-to-br from-primary-50/30 via-white to-primary-100/20">
        {/* Compact Hero Section - Matching Website Design */}
        <section className="bg-white pt-20 pb-4" style={{ paddingTop: '4rem' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center mb-3">
              <span className="text-3xl mr-3">🎓</span>
              <h1 style={{
                fontSize: '24px',
                marginBottom: '0',
                color: '#2563eb',
                fontWeight: '700',
                textTransform: 'capitalize'
              }}>
                Bourses d'Études de {config.level}
              </h1>
            </div>

            <div className="archive-description">
              <p style={{
                marginBottom: '15px',
                textAlign: 'justify',
                color: '#3d3d3d',
                fontSize: '17px',
                lineHeight: '30px',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif'
              }}>
                Découvrez notre collection exclusive de bourses d'études de {config.level} pour l'année académique 2025-2026.
                Ces opportunités exceptionnelles sont proposées par des institutions gouvernementales prestigieuses,
                des universités de renommée mondiale, et des fondations dédiées à l'excellence académique.
              </p>
              <p style={{
                marginBottom: '20px',
                textAlign: 'justify',
                color: '#3d3d3d',
                fontSize: '17px',
                lineHeight: '30px',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif'
              }}>
                Que vous visiez l'Europe prestigieuse, l'Amérique du Nord innovante, l'Asie dynamique, ou l'Océanie accueillante,
                chaque programme offre un financement complet ou partiel adapté à vos ambitions académiques.
                Explorez, comparez et trouvez la bourse qui transformera votre parcours éducatif.
              </p>
            </div>

            {/* Quick Stats */}
            <div className="flex flex-wrap gap-3 mt-3">
              <div className="flex items-center bg-blue-50 px-3 py-1.5 rounded-lg">
                <svg className="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                <span className="text-sm font-medium text-blue-800">Programmes Accrédités</span>
              </div>
              <div className="flex items-center bg-green-50 px-3 py-1.5 rounded-lg">
                <svg className="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
                <span className="text-sm font-medium text-green-800">Financement Complet</span>
              </div>
              <div className="flex items-center bg-purple-50 px-3 py-1.5 rounded-lg">
                <svg className="w-4 h-4 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm font-medium text-purple-800">Destinations Mondiales</span>
              </div>
            </div>
          </div>
        </section>

        {/* Desktop Ad - Only visible on large screens */}
        <div className="hidden lg:block py-8 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <AdPlacement
              adSlot="1234567890"
              adSize="leaderboard"
              responsive={true}
            />
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          {/* Scholarships Grid - Clean & Focused */}
          <div id="scholarships-section" className="mb-8">


                {loading ? (
                  <div className="flex justify-center items-center py-16">
                    <Spin size="large" tip="Chargement des bourses..." />
                  </div>
                ) : error ? (
                  <Alert
                    message="Erreur"
                    description={error}
                    type="error"
                    showIcon
                    className="mb-6 rounded-xl shadow-md"
                  />
                ) : (
                  <>
                    {/* Mobile Ad - Only visible on small screens */}
                    <div className="mb-8 md:hidden">
                      <AdPlacement
                        adSlot="4567890123"
                        adSize="rectangle"
                        responsive={true}
                        fullWidth={true}
                      />
                    </div>

                    <div className="gy-pcard-wrap">
                      {scholarships.map((scholarship, index) => (
                        <EnhancedScholarshipCard
                          key={scholarship.id}
                          id={scholarship.id}
                          title={scholarship.title}
                          thumbnail={scholarship.thumbnail}
                          deadline={scholarship.deadline}
                          isOpen={scholarship.isOpen}
                          level={scholarship.level}
                          country={scholarship.country}
                          fundingSource={scholarship.fundingSource}
                          onClick={handleScholarshipClick}
                          index={index}
                          variant="greatyop"
                        />
                      ))}
                    </div>

                    {/* GreatYOP Pagination */}
                    {(pagination.total > pagination.limit || scholarships.length > 0) && (
                      <GreatYOPPagination
                        current={pagination.page}
                        total={Math.max(pagination.total, scholarships.length)}
                        pageSize={pagination.limit}
                        onChange={handlePageChange}
                        showQuickJumper={false}
                      />
                    )}
                  </>
                )}
          </div>
        </div>

        {/* Info Section - Fully Responsive */}
        <div className="bg-white py-4 md:py-6 lg:py-8 border-t border-gray-100">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto">
              <div id="info-section">
                <h2 className="text-base md:text-lg lg:text-xl font-semibold text-gray-900 mb-3 md:mb-4">
                  {config.infoTitle}
                </h2>

                <p className="text-gray-700 leading-relaxed mb-4 md:mb-6 text-sm md:text-base text-justify">
                  {config.infoContent}
                </p>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 md:gap-3">
                  {config.benefits.map((benefit, index) => (
                    <div key={index} className="flex items-start space-x-2 md:space-x-3">
                      <svg className="w-3 h-3 md:w-4 md:h-4 text-green-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700 text-xs md:text-sm leading-relaxed">
                        {benefit}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Comments Section */}
        <div className="bg-gray-50 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <CommentsSection
              pageType="level"
              pageId={config.level}
            />
          </div>
        </div>


      </div>
    </>
  );
};

export default StandardLevelPage;
