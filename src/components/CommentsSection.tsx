import React, { useState, useEffect } from 'react';
import { Button, Input, Avatar, message, Spin } from 'antd';
import { UserOutlined, SendOutlined } from '@ant-design/icons';

const { TextArea } = Input;

interface Comment {
  id: number;
  author: string;
  content: string;
  timestamp: string;
  isAdmin: boolean;
  replies?: Comment[];
}

interface CommentsSectionProps {
  pageType: string;
  pageId: string;
}

const CommentsSection: React.FC<CommentsSectionProps> = ({ pageType, pageId }) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [userName, setUserName] = useState('');
  const [replyingTo, setReplyingTo] = useState<number | null>(null);
  const [replyContent, setReplyContent] = useState('');

  useEffect(() => {
    fetchComments();
  }, [pageType, pageId]);

  const fetchComments = async () => {
    try {
      setLoading(true);
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/comments/${pageType}/${pageId}`);
      
      if (response.ok) {
        const data = await response.json();
        setComments(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
      // Set some mock data for demonstration
      setComments([
        {
          id: 1,
          author: 'Ahmed Benali',
          content: 'Bonjour, j\'aimerais savoir quelles sont les conditions d\'éligibilité pour les bourses de licence au Canada?',
          timestamp: '2024-01-15T10:30:00Z',
          isAdmin: false,
          replies: [
            {
              id: 2,
              author: 'Admin MaBourse',
              content: 'Bonjour Ahmed, pour les bourses de licence au Canada, vous devez généralement avoir un excellent dossier académique (moyenne supérieure à 16/20), une maîtrise de l\'anglais ou du français selon la province, et parfois une expérience associative. Je vous recommande de consulter notre guide détaillé sur les bourses canadiennes.',
              timestamp: '2024-01-15T14:20:00Z',
              isAdmin: true
            }
          ]
        },
        {
          id: 3,
          author: 'Fatima Zahra',
          content: 'Est-ce que les bourses couvrent aussi les frais de logement et de nourriture?',
          timestamp: '2024-01-16T09:15:00Z',
          isAdmin: false,
          replies: [
            {
              id: 4,
              author: 'Admin MaBourse',
              content: 'Bonjour Fatima, cela dépend du type de bourse. Les bourses complètes (fully funded) couvrent généralement les frais de scolarité, logement, nourriture et parfois même les frais de voyage. Les bourses partielles ne couvrent qu\'une partie de ces frais. Chaque bourse a ses propres conditions que vous pouvez consulter dans la description détaillée.',
              timestamp: '2024-01-16T11:45:00Z',
              isAdmin: true
            }
          ]
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitComment = async () => {
    if (!newComment.trim() || !userName.trim()) {
      message.error('Veuillez remplir tous les champs');
      return;
    }

    setSubmitting(true);
    try {
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pageType,
          pageId,
          author: userName,
          content: newComment
        }),
      });

      if (response.ok) {
        message.success('Commentaire ajouté avec succès!');
        setNewComment('');
        fetchComments(); // Refresh comments
      } else {
        message.error('Erreur lors de l\'ajout du commentaire');
      }
    } catch (error) {
      console.error('Error submitting comment:', error);
      message.error('Erreur lors de l\'ajout du commentaire');
    } finally {
      setSubmitting(false);
    }
  };

  const handleSubmitReply = async (parentId: number) => {
    if (!replyContent.trim() || !userName.trim()) {
      message.error('Veuillez remplir tous les champs');
      return;
    }

    setSubmitting(true);
    try {
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/comments/reply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          parentId,
          author: userName,
          content: replyContent
        }),
      });

      if (response.ok) {
        message.success('Réponse ajoutée avec succès!');
        setReplyContent('');
        setReplyingTo(null);
        fetchComments(); // Refresh comments
      } else {
        message.error('Erreur lors de l\'ajout de la réponse');
      }
    } catch (error) {
      console.error('Error submitting reply:', error);
      message.error('Erreur lors de l\'ajout de la réponse');
    } finally {
      setSubmitting(false);
    }
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="text-center">
          <Spin size="small" tip="Chargement..." />
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-1">
          Discussion et Questions
        </h3>
        <p className="text-sm text-gray-600">
          Posez vos questions sur les bourses d'études.
        </p>
      </div>

      {/* Add New Comment */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-2">
          Poser une question
        </h4>
        <div className="space-y-2">
          <Input
            placeholder="Votre nom"
            value={userName}
            onChange={(e) => setUserName(e.target.value)}
            prefix={<UserOutlined />}
            size="small"
          />
          <TextArea
            placeholder="Votre question..."
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            rows={2}
            size="small"
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSubmitComment}
            loading={submitting}
            size="small"
            className="bg-blue-600 hover:bg-blue-700"
          >
            Publier
          </Button>
        </div>
      </div>

      {/* Comments List */}
      <div className="space-y-3">
        {comments.length > 0 ? (
          comments.map((comment) => (
            <div key={comment.id} className="border-l-2 border-blue-200 pl-3">
              <div className="flex items-start space-x-2">
                <Avatar
                  size="small"
                  icon={<UserOutlined />}
                  className={comment.isAdmin ? 'bg-green-600' : 'bg-blue-600'}
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="font-medium text-gray-900 text-sm">
                      {comment.author}
                    </span>
                    {comment.isAdmin && (
                      <span className="px-1 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded">
                        Admin
                      </span>
                    )}
                    <span className="text-xs text-gray-500">
                      {formatDate(comment.timestamp)}
                    </span>
                  </div>
                  <p className="text-gray-700 mb-2 text-sm leading-relaxed">
                    {comment.content}
                  </p>
                  
                  {/* Replies */}
                  {comment.replies && comment.replies.length > 0 && (
                    <div className="mt-2 space-y-2">
                      {comment.replies.map((reply) => (
                        <div key={reply.id} className="flex items-start space-x-2 bg-gray-50 p-2 rounded">
                          <Avatar
                            size="small"
                            icon={<UserOutlined />}
                            className={reply.isAdmin ? 'bg-green-600' : 'bg-gray-600'}
                          />
                          <div className="flex-1">
                            <div className="flex items-center space-x-1 mb-1">
                              <span className="font-medium text-gray-900 text-xs">
                                {reply.author}
                              </span>
                              {reply.isAdmin && (
                                <span className="px-1 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded">
                                  Admin
                                </span>
                              )}
                              <span className="text-xs text-gray-500">
                                {formatDate(reply.timestamp)}
                              </span>
                            </div>
                            <p className="text-gray-700 text-xs leading-relaxed">
                              {reply.content}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-6">
            <div className="text-gray-400 mb-2">
              <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h4 className="text-sm font-medium text-gray-900 mb-1">
              Aucune question pour le moment
            </h4>
            <p className="text-xs text-gray-500">
              Soyez le premier à poser une question.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CommentsSection;
