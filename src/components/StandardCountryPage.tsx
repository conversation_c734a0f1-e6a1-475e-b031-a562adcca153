import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../context/LanguageContext';
import EnhancedScholarshipCard from './EnhancedScholarshipCard';
import GreatYOPPagination from './GreatYOPPagination';


import AdPlacement from './AdPlacement';
import { Pagination, Spin, Alert } from 'antd';

interface Scholarship {
  id: number;
  title: string;
  description: string;
  level: string;
  country: string;
  deadline: string;
  isOpen: boolean;
  thumbnail: string;
  fundingSource?: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface Country {
  country: string;
  count: number;
}

interface CountryPageConfig {
  country: string;
  title: string;
  description: string;
  keywords: string;
  infoTitle: string;
  infoContent: string;
  benefits: string[];
  apiEndpoint: string;
  flag?: string;
}

interface StandardCountryPageProps {
  config: CountryPageConfig;
}

const StandardCountryPage: React.FC<StandardCountryPageProps> = ({ config }) => {
  const { translations } = useLanguage();
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [countries, setCountries] = useState<Country[]>([]);
  const [countriesLoading, setCountriesLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 6,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });

  // Helper function to get country flag - Complete World Countries Database
  const getCountryFlag = (countryName: string): string => {
    const flagMap: { [key: string]: string } = {
      // === EUROPE ===
      'France': '🇫🇷', 'Allemagne': '🇩🇪', 'Germany': '🇩🇪',
      'Royaume-Uni': '🇬🇧', 'United Kingdom': '🇬🇧', 'UK': '🇬🇧',
      'Italie': '🇮🇹', 'Italy': '🇮🇹',
      'Espagne': '🇪🇸', 'Spain': '🇪🇸',
      'Portugal': '🇵🇹',
      'Pays-Bas': '🇳🇱', 'Netherlands': '🇳🇱',
      'Belgique': '🇧🇪', 'Belgium': '🇧🇪',
      'Suisse': '🇨🇭', 'Switzerland': '🇨🇭',
      'Autriche': '🇦🇹', 'Austria': '🇦🇹',
      'Suède': '🇸🇪', 'Sweden': '🇸🇪',
      'Norvège': '🇳🇴', 'Norway': '🇳🇴',
      'Danemark': '🇩🇰', 'Denmark': '🇩🇰',
      'Finlande': '🇫🇮', 'Finland': '🇫🇮',
      'Pologne': '🇵🇱', 'Poland': '🇵🇱',
      'République tchèque': '🇨🇿', 'Czech Republic': '🇨🇿',
      'Hongrie': '🇭🇺', 'Hungary': '🇭🇺',
      'Slovaquie': '🇸🇰', 'Slovakia': '🇸🇰',
      'Slovénie': '🇸🇮', 'Slovenia': '🇸🇮',
      'Croatie': '🇭🇷', 'Croatia': '🇭🇷',
      'Roumanie': '🇷🇴', 'Romania': '🇷🇴',
      'Bulgarie': '🇧🇬', 'Bulgaria': '🇧🇬',
      'Grèce': '🇬🇷', 'Greece': '🇬🇷',
      'Turquie': '🇹🇷', 'Turkey': '🇹🇷',
      'Russie': '🇷🇺', 'Russia': '🇷🇺',
      'Ukraine': '🇺🇦',
      'Biélorussie': '🇧🇾', 'Belarus': '🇧🇾',
      'Estonie': '🇪🇪', 'Estonia': '🇪🇪',
      'Lettonie': '🇱🇻', 'Latvia': '🇱🇻',
      'Lituanie': '🇱🇹', 'Lithuania': '🇱🇹',
      'Irlande': '🇮🇪', 'Ireland': '🇮🇪',
      'Islande': '🇮🇸', 'Iceland': '🇮🇸',
      'Luxembourg': '🇱🇺',
      'Malte': '🇲🇹', 'Malta': '🇲🇹',
      'Chypre': '🇨🇾', 'Cyprus': '🇨🇾',
      'Serbie': '🇷🇸', 'Serbia': '🇷🇸',
      'Monténégro': '🇲🇪', 'Montenegro': '🇲🇪',
      'Bosnie-Herzégovine': '🇧🇦', 'Bosnia and Herzegovina': '🇧🇦',
      'Macédoine du Nord': '🇲🇰', 'North Macedonia': '🇲🇰',
      'Albanie': '🇦🇱', 'Albania': '🇦🇱',
      'Kosovo': '🇽🇰',
      'Moldavie': '🇲🇩', 'Moldova': '🇲🇩',

      // === AMERICAS ===
      'États-Unis': '🇺🇸', 'United States': '🇺🇸', 'USA': '🇺🇸',
      'Canada': '🇨🇦',
      'Mexique': '🇲🇽', 'Mexico': '🇲🇽',
      'Brésil': '🇧🇷', 'Brazil': '🇧🇷',
      'Argentine': '🇦🇷', 'Argentina': '🇦🇷',
      'Chili': '🇨🇱', 'Chile': '🇨🇱',
      'Colombie': '🇨🇴', 'Colombia': '🇨🇴',
      'Pérou': '🇵🇪', 'Peru': '🇵🇪',
      'Venezuela': '🇻🇪',
      'Équateur': '🇪🇨', 'Ecuador': '🇪🇨',
      'Bolivie': '🇧🇴', 'Bolivia': '🇧🇴',
      'Paraguay': '🇵🇾',
      'Uruguay': '🇺🇾',
      'Guyana': '🇬🇾',
      'Suriname': '🇸🇷',
      'Guyane française': '🇬🇫', 'French Guiana': '🇬🇫',
      'Cuba': '🇨🇺',
      'Jamaïque': '🇯🇲', 'Jamaica': '🇯🇲',
      'Haïti': '🇭🇹', 'Haiti': '🇭🇹',
      'République dominicaine': '🇩🇴', 'Dominican Republic': '🇩🇴',
      'Porto Rico': '🇵🇷', 'Puerto Rico': '🇵🇷',
      'Costa Rica': '🇨🇷',
      'Panama': '🇵🇦',
      'Nicaragua': '🇳🇮',
      'Honduras': '🇭🇳',
      'El Salvador': '🇸🇻',
      'Guatemala': '🇬🇹',
      'Belize': '🇧🇿',

      // === ASIA ===
      'Chine': '🇨🇳', 'China': '🇨🇳',
      'Japon': '🇯🇵', 'Japan': '🇯🇵',
      'Corée du Sud': '🇰🇷', 'South Korea': '🇰🇷',
      'Corée du Nord': '🇰🇵', 'North Korea': '🇰🇵',
      'Inde': '🇮🇳', 'India': '🇮🇳',
      'Pakistan': '🇵🇰',
      'Bangladesh': '🇧🇩',
      'Sri Lanka': '🇱🇰',
      'Népal': '🇳🇵', 'Nepal': '🇳🇵',
      'Bhoutan': '🇧🇹', 'Bhutan': '🇧🇹',
      'Maldives': '🇲🇻',
      'Afghanistan': '🇦🇫',
      'Iran': '🇮🇷',
      'Irak': '🇮🇶', 'Iraq': '🇮🇶',
      'Syrie': '🇸🇾', 'Syria': '🇸🇾',
      'Liban': '🇱🇧', 'Lebanon': '🇱🇧',
      'Jordanie': '🇯🇴', 'Jordan': '🇯🇴',
      'Israël': '🇮🇱', 'Israel': '🇮🇱',
      'Palestine': '🇵🇸',
      'Arabie saoudite': '🇸🇦', 'Saudi Arabia': '🇸🇦',
      'Émirats arabes unis': '🇦🇪', 'UAE': '🇦🇪', 'United Arab Emirates': '🇦🇪',
      'Qatar': '🇶🇦',
      'Koweït': '🇰🇼', 'Kuwait': '🇰🇼',
      'Bahreïn': '🇧🇭', 'Bahrain': '🇧🇭',
      'Oman': '🇴🇲',
      'Yémen': '🇾🇪', 'Yemen': '🇾🇪',
      'Thaïlande': '🇹🇭', 'Thailand': '🇹🇭',
      'Vietnam': '🇻🇳',
      'Malaisie': '🇲🇾', 'Malaysia': '🇲🇾',
      'Singapour': '🇸🇬', 'Singapore': '🇸🇬',
      'Indonésie': '🇮🇩', 'Indonesia': '🇮🇩',
      'Philippines': '🇵🇭',
      'Brunei': '🇧🇳',
      'Cambodge': '🇰🇭', 'Cambodia': '🇰🇭',
      'Laos': '🇱🇦',
      'Myanmar': '🇲🇲', 'Birmanie': '🇲🇲', 'Burma': '🇲🇲',
      'Mongolie': '🇲🇳', 'Mongolia': '🇲🇳',
      'Kazakhstan': '🇰🇿',
      'Kirghizistan': '🇰🇬', 'Kyrgyzstan': '🇰🇬',
      'Tadjikistan': '🇹🇯', 'Tajikistan': '🇹🇯',
      'Turkménistan': '🇹🇲', 'Turkmenistan': '🇹🇲',
      'Ouzbékistan': '🇺🇿', 'Uzbekistan': '🇺🇿',
      'Géorgie': '🇬🇪', 'Georgia': '🇬🇪',
      'Arménie': '🇦🇲', 'Armenia': '🇦🇲',
      'Azerbaïdjan': '🇦🇿', 'Azerbaijan': '🇦🇿',
      'Taïwan': '🇹🇼', 'Taiwan': '🇹🇼',
      'Hong Kong': '🇭🇰',
      'Macao': '🇲🇴', 'Macau': '🇲🇴',

      // === AFRICA ===
      'Égypte': '🇪🇬', 'Egypt': '🇪🇬',
      'Libye': '🇱🇾', 'Libya': '🇱🇾',
      'Tunisie': '🇹🇳', 'Tunisia': '🇹🇳',
      'Algérie': '🇩🇿', 'Algeria': '🇩🇿',
      'Maroc': '🇲🇦', 'Morocco': '🇲🇦',
      'Soudan': '🇸🇩', 'Sudan': '🇸🇩',
      'Soudan du Sud': '🇸🇸', 'South Sudan': '🇸🇸',
      'Éthiopie': '🇪🇹', 'Ethiopia': '🇪🇹',
      'Érythrée': '🇪🇷', 'Eritrea': '🇪🇷',
      'Djibouti': '🇩🇯',
      'Somalie': '🇸🇴', 'Somalia': '🇸🇴',
      'Kenya': '🇰🇪',
      'Ouganda': '🇺🇬', 'Uganda': '🇺🇬',
      'Tanzanie': '🇹🇿', 'Tanzania': '🇹🇿',
      'Rwanda': '🇷🇼',
      'Burundi': '🇧🇮',
      'République démocratique du Congo': '🇨🇩', 'Democratic Republic of Congo': '🇨🇩', 'DRC': '🇨🇩',
      'République du Congo': '🇨🇬', 'Republic of Congo': '🇨🇬',
      'République centrafricaine': '🇨🇫', 'Central African Republic': '🇨🇫',
      'Tchad': '🇹🇩', 'Chad': '🇹🇩',
      'Cameroun': '🇨🇲', 'Cameroon': '🇨🇲',
      'Nigeria': '🇳🇬',
      'Niger': '🇳🇪',
      'Mali': '🇲🇱',
      'Burkina Faso': '🇧🇫',
      'Sénégal': '🇸🇳', 'Senegal': '🇸🇳',
      'Gambie': '🇬🇲', 'Gambia': '🇬🇲',
      'Guinée-Bissau': '🇬🇼', 'Guinea-Bissau': '🇬🇼',
      'Guinée': '🇬🇳', 'Guinea': '🇬🇳',
      'Sierra Leone': '🇸🇱',
      'Liberia': '🇱🇷',
      'Côte d\'Ivoire': '🇨🇮', 'Ivory Coast': '🇨🇮',
      'Ghana': '🇬🇭',
      'Togo': '🇹🇬',
      'Bénin': '🇧🇯', 'Benin': '🇧🇯',
      'Gabon': '🇬🇦',
      'Guinée équatoriale': '🇬🇶', 'Equatorial Guinea': '🇬🇶',
      'São Tomé-et-Príncipe': '🇸🇹', 'Sao Tome and Principe': '🇸🇹',
      'Angola': '🇦🇴',
      'Zambie': '🇿🇲', 'Zambia': '🇿🇲',
      'Zimbabwe': '🇿🇼',
      'Botswana': '🇧🇼',
      'Namibie': '🇳🇦', 'Namibia': '🇳🇦',
      'Afrique du Sud': '🇿🇦', 'South Africa': '🇿🇦',
      'Lesotho': '🇱🇸',
      'Eswatini': '🇸🇿', 'Swaziland': '🇸🇿',
      'Mozambique': '🇲🇿',
      'Malawi': '🇲🇼',
      'Madagascar': '🇲🇬',
      'Maurice': '🇲🇺', 'Mauritius': '🇲🇺',
      'Seychelles': '🇸🇨',
      'Comores': '🇰🇲', 'Comoros': '🇰🇲',
      'Mayotte': '🇾🇹',
      'Réunion': '🇷🇪', 'Reunion': '🇷🇪',
      'Mauritanie': '🇲🇷', 'Mauritania': '🇲🇷',
      'Cap-Vert': '🇨🇻', 'Cape Verde': '🇨🇻',

      // === OCEANIA ===
      'Australie': '🇦🇺', 'Australia': '🇦🇺',
      'Nouvelle-Zélande': '🇳🇿', 'New Zealand': '🇳🇿',
      'Papouasie-Nouvelle-Guinée': '🇵🇬', 'Papua New Guinea': '🇵🇬',
      'Fidji': '🇫🇯', 'Fiji': '🇫🇯',
      'Vanuatu': '🇻🇺',
      'Nouvelle-Calédonie': '🇳🇨', 'New Caledonia': '🇳🇨',
      'Polynésie française': '🇵🇫', 'French Polynesia': '🇵🇫',
      'Samoa': '🇼🇸',
      'Tonga': '🇹🇴',
      'Kiribati': '🇰🇮',
      'Tuvalu': '🇹🇻',
      'Nauru': '🇳🇷',
      'Palau': '🇵🇼',
      'États fédérés de Micronésie': '🇫🇲', 'Micronesia': '🇫🇲',
      'Îles Marshall': '🇲🇭', 'Marshall Islands': '🇲🇭',
      'Îles Salomon': '🇸🇧', 'Solomon Islands': '🇸🇧',
      'Îles Cook': '🇨🇰', 'Cook Islands': '🇨🇰',
      'Niue': '🇳🇺',
      'Tokelau': '🇹🇰'
    };
    return flagMap[countryName] || '🌍';
  };

  // Handle scholarship card click
  const handleScholarshipClick = (id: number, slug?: string) => {
    if (slug) {
      window.location.href = `/bourse/${slug}`;
    } else {
      window.location.href = `/scholarships/${id}`;
    }
  };

  const fetchScholarships = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        country: config.country,
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      });

      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${config.apiEndpoint}?${params}`);

      if (!response.ok) {
        throw new Error('Failed to fetch scholarships');
      }

      const data = await response.json();

      if (data.success) {
        setScholarships(data.data || []);
        setPagination(prev => ({
          ...prev,
          total: data.pagination?.total || 0,
          totalPages: data.pagination?.totalPages || 0,
          hasNextPage: data.pagination?.hasNextPage || false,
          hasPreviousPage: data.pagination?.hasPreviousPage || false
        }));
      } else {
        throw new Error(data.message || 'Failed to load scholarships');
      }
    } catch (error) {
      console.error('Error fetching scholarships:', error);
      setError('Impossible de charger les bourses. Veuillez réessayer plus tard.');
      setScholarships([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchCountries = async () => {
    try {
      setCountriesLoading(true);

      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
      const response = await fetch(`${apiUrl}/api/countries`);

      if (!response.ok) {
        throw new Error('Failed to fetch countries');
      }

      const result = await response.json();
      const countriesData = result.data || result;

      // Filter out current country and sort by scholarship count
      const filteredCountries = countriesData
        .filter((country: Country) => country.country !== config.country)
        .sort((a: Country, b: Country) => b.count - a.count)
        .slice(0, 12); // Show top 12 countries

      setCountries(filteredCountries);
    } catch (error) {
      console.error('Error fetching countries:', error);
      setCountries([]);
    } finally {
      setCountriesLoading(false);
    }
  };

  useEffect(() => {
    fetchScholarships();
    fetchCountries();
  }, [pagination.page, config.country]);

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };



  return (
    <>
      <Helmet>
        <title>{config.title}</title>
        <meta name="description" content={config.description} />
        <meta name="keywords" content={config.keywords} />
      </Helmet>

      <div className="min-h-screen bg-white">
        {/* Enhanced Hero Section */}
        <section className="bg-white pt-24 pb-6" style={{ paddingTop: '6rem' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center mb-4">
              <span className="text-3xl mr-3">{config.flag}</span>
              <h1 style={{
                fontSize: '24px',
                marginBottom: '0',
                color: '#2563eb',
                fontWeight: '700',
                textTransform: 'capitalize'
              }}>
                Bourses d'Études en {config.country}
              </h1>
            </div>

            <div className="archive-description">
              <p style={{
                marginBottom: '18px',
                textAlign: 'justify',
                fontSize: '16px',
                lineHeight: '1.6',
                color: '#374151'
              }}>
                Découvrez les meilleures opportunités de bourses d'études en {config.country}, un pays reconnu pour son excellence académique et ses programmes d'enseignement supérieur de renommée mondiale. Que vous recherchiez une bourse complète ou partielle, explorez nos offres soigneusement sélectionnées.
              </p>
              <p style={{
                marginBottom: '20px',
                textAlign: 'justify',
                fontSize: '15px',
                lineHeight: '1.5',
                color: '#6b7280'
              }}>
                Chaque bourse présente des critères d'éligibilité spécifiques, des avantages financiers détaillés et des procédures de candidature claires. Cliquez sur une bourse pour accéder aux informations complètes et commencer votre parcours académique en {config.country}.
              </p>

              {/* Quick Stats */}
              <div className="flex flex-wrap gap-4 mt-4">
                <div className="flex items-center bg-blue-50 px-3 py-2 rounded-lg">
                  <svg className="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                  <span className="text-sm font-medium text-blue-800">Programmes Accrédités</span>
                </div>
                <div className="flex items-center bg-green-50 px-3 py-2 rounded-lg">
                  <svg className="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                  <span className="text-sm font-medium text-green-800">Financement Disponible</span>
                </div>
                <div className="flex items-center bg-purple-50 px-3 py-2 rounded-lg">
                  <svg className="w-4 h-4 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm font-medium text-purple-800">Opportunités Internationales</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Desktop Ad - Only visible on large screens */}
        <div className="hidden lg:block py-6 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <AdPlacement
              adSlot="1234567890"
              adSize="leaderboard"
              responsive={true}
            />
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="w-full">{/* Full width layout without sidebar */}

              {/* Scholarships Grid */}
              <div id="scholarships-section" className="mb-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-2xl font-bold text-gray-900">
                    {config.country}
                  </h2>
                  <div className="text-sm text-gray-600">
                    {!loading && !error && `${pagination.total} résultats`}
                  </div>
                </div>

                {loading ? (
                  <div className="flex justify-center items-center py-16">
                    <Spin size="large" tip="Chargement des bourses..." />
                  </div>
                ) : error ? (
                  <Alert
                    message="Erreur"
                    description={error}
                    type="error"
                    showIcon
                    className="mb-6 rounded-xl shadow-md"
                  />
                ) : (
                  <>
                    {/* Mobile Ad - Only visible on small screens */}
                    <div className="mb-8 md:hidden">
                      <AdPlacement
                        adSlot="4567890123"
                        adSize="rectangle"
                        responsive={true}
                        fullWidth={true}
                      />
                    </div>

                    <div className="gy-pcard-wrap">
                      {scholarships.map((scholarship, index) => (
                        <EnhancedScholarshipCard
                          key={scholarship.id}
                          id={scholarship.id}
                          title={scholarship.title}
                          thumbnail={scholarship.thumbnail}
                          deadline={scholarship.deadline}
                          isOpen={scholarship.isOpen}
                          level={scholarship.level}
                          country={scholarship.country}
                          fundingSource={scholarship.fundingSource}
                          onClick={handleScholarshipClick}
                          index={index}
                          variant="greatyop"
                        />
                      ))}
                    </div>

                    {/* GreatYOP Pagination */}
                    {pagination.total > 0 && (
                      <GreatYOPPagination
                        current={pagination.page}
                        total={pagination.total}
                        pageSize={pagination.limit}
                        onChange={handlePageChange}
                        showQuickJumper={false}
                      />
                    )}
                  </>
                )}
              </div>
            </div>
        </div>

        {/* Other Countries Section */}
        <section className="py-12 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-10">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Explorez d'Autres Destinations
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Découvrez des opportunités de bourses d'études dans d'autres pays du monde.
                Chaque destination offre des programmes uniques et des expériences académiques enrichissantes.
              </p>
            </div>

            {countriesLoading ? (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {[...Array(16)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="flex items-center space-x-3">
                        <div className="h-6 w-6 bg-gray-200 rounded"></div>
                        <div className="h-4 bg-gray-200 rounded flex-1"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : countries.length > 0 ? (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {countries.map((country) => (
                    <a
                      key={country.country}
                      href={`/pays/${encodeURIComponent(country.country)}`}
                      className="group flex items-center space-x-3 p-2 rounded-md hover:bg-gray-50 transition-colors duration-200"
                    >
                      <span className="text-lg group-hover:scale-110 transition-transform duration-200">
                        {getCountryFlag(country.country)}
                      </span>
                      <span className="text-sm font-medium text-gray-700 group-hover:text-primary transition-colors duration-200 truncate">
                        {country.country}
                      </span>
                    </a>
                  ))}
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
                <div className="text-4xl mb-4">🌍</div>
                <p className="text-gray-500">Aucun autre pays disponible pour le moment.</p>
              </div>
            )}

            {/* View All Countries Button */}
            <div className="text-center mt-8">
              <a
                href="/countries"
                className="inline-flex items-center px-6 py-3 bg-primary text-white font-medium rounded-lg hover:bg-primary-dark transition-colors duration-200 shadow-md hover:shadow-lg"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Voir Tous les Pays
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>
          </div>
        </section>

        {/* Newsletter Section - Compact and Professional */}
        <section className="relative py-8 overflow-hidden">
          {/* Background with gradient and pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary-dark to-primary">
            <div className="absolute inset-0 opacity-10">
              <svg width="100%" height="100%">
                <defs>
                  <pattern id="smallGrid" width="20" height="20" patternUnits="userSpaceOnUse">
                    <path d="M 20 0 L 0 0 0 20" fill="none" stroke="white" strokeWidth="0.5" />
                  </pattern>
                  <pattern id="grid" width="80" height="80" patternUnits="userSpaceOnUse">
                    <rect width="80" height="80" fill="url(#smallGrid)" />
                    <path d="M 80 0 L 0 0 0 80" fill="none" stroke="white" strokeWidth="1" />
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
              </svg>
            </div>
          </div>
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 -mt-20 -mr-20 w-80 h-80 rounded-full bg-white opacity-10 blur-3xl"></div>
          <div className="absolute bottom-0 left-0 -mb-20 -ml-20 w-80 h-80 rounded-full bg-white opacity-10 blur-3xl"></div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="grid grid-cols-1 lg:grid-cols-5">
                {/* Left column - Image */}
                <div className="relative hidden lg:block lg:col-span-2">
                  <img
                    src="/assets/newsletter-image.jpg"
                    alt="Student reading"
                    className="absolute inset-0 h-full w-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = 'https://images.unsplash.com/photo-1523240795612-9a054b0db644?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800';
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-r from-primary-dark/80 to-transparent mix-blend-multiply"></div>
                  <div className="absolute inset-0 flex items-center justify-center p-6">
                    <div className="text-white">
                      <h3 className="text-lg font-bold mb-3">Restez Informé</h3>
                      <ul className="space-y-2 text-sm">
                        {[
                          `Bourses pour ${config.country}`,
                          'Dates limites importantes',
                          'Conseils exclusifs',
                          'Témoignages d\'étudiants'
                        ].map((benefit, index) => (
                          <li key={index} className="flex items-start">
                            <svg className="h-4 w-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                            <span className="leading-tight">{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Right column - Form */}
                <div className="p-6 lg:col-span-3">
                  <div className="flex items-center mb-4">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 mr-3">
                      <svg className="w-5 h-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-gray-900">Newsletter Bourses</h2>
                      <p className="text-sm text-gray-600">Opportunités d'études en {config.country}</p>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 mb-4 leading-relaxed">
                    Recevez les dernières opportunités de bourses pour {config.country} directement dans votre boîte mail.
                    Nous ne vous enverrons pas de spam.
                  </p>

                  <form className="space-y-4">
                    <div className="flex gap-3">
                      <input
                        type="email"
                        placeholder="<EMAIL>"
                        className="flex-1 px-4 py-3 text-sm rounded-lg border border-gray-300 focus:ring-primary focus:border-primary focus:outline-none focus:ring-2 transition-colors duration-200"
                      />
                      <button
                        type="submit"
                        className="px-6 py-3 bg-primary text-white text-sm font-medium rounded-lg hover:bg-primary-dark transition-colors duration-300 whitespace-nowrap"
                      >
                        S'abonner
                      </button>
                    </div>

                    <div className="flex items-start">
                      <input
                        id="privacy"
                        type="checkbox"
                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-0.5"
                      />
                      <label htmlFor="privacy" className="ml-3 block text-sm text-gray-600 leading-relaxed">
                        J'accepte de recevoir des emails concernant les bourses d'études et opportunités académiques
                      </label>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default StandardCountryPage;
