import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';
import PageEndSuggestions from '../components/PageEndSuggestions';
import GreatYOPPagination from '../components/GreatYOPPagination';

interface Country {
  country: string;
  count: number;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

const Countries: React.FC = () => {
  const { translations } = useLanguage();
  const [countries, setCountries] = useState<Country[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCountries, setFilteredCountries] = useState<Country[]>([]);
  const [displayedCountries, setDisplayedCountries] = useState<Country[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 15, // 5x3 grid = 15 countries per page
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });

  useEffect(() => {
    fetchCountries();
  }, []);

  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredCountries(countries);
    } else {
      const filtered = countries.filter(country =>
        country.country.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredCountries(filtered);
    }
  }, [searchTerm, countries]);

  // Update pagination and displayed countries when filtered countries change
  useEffect(() => {
    const total = filteredCountries.length;
    const totalPages = Math.ceil(total / pagination.limit);

    setPagination(prev => ({
      ...prev,
      total,
      totalPages,
      hasNextPage: prev.page < totalPages,
      hasPreviousPage: prev.page > 1
    }));

    // Calculate displayed countries for current page
    const startIndex = (pagination.page - 1) * pagination.limit;
    const endIndex = startIndex + pagination.limit;
    setDisplayedCountries(filteredCountries.slice(startIndex, endIndex));
  }, [filteredCountries, pagination.page, pagination.limit]);

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const fetchCountries = async () => {
    try {
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/countries`);
      if (response.ok) {
        const data = await response.json();
        setCountries(data.data || []);
      } else {
        console.error('Failed to fetch countries');
      }
    } catch (error) {
      console.error('Error fetching countries:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCountryFlag = (countryName: string): string => {
    const flagMap: { [key: string]: string } = {
      // === EUROPE ===
      'France': '🇫🇷', 'Allemagne': '🇩🇪', 'Germany': '🇩🇪',
      'Royaume-Uni': '🇬🇧', 'United Kingdom': '🇬🇧', 'UK': '🇬🇧',
      'Italie': '🇮🇹', 'Italy': '🇮🇹',
      'Espagne': '🇪🇸', 'Spain': '🇪🇸',
      'Portugal': '🇵🇹',
      'Pays-Bas': '🇳🇱', 'Netherlands': '🇳🇱',
      'Belgique': '🇧🇪', 'Belgium': '🇧🇪',
      'Suisse': '🇨🇭', 'Switzerland': '🇨🇭',
      'Autriche': '🇦🇹', 'Austria': '🇦🇹',
      'Suède': '🇸🇪', 'Sweden': '🇸🇪',
      'Norvège': '🇳🇴', 'Norway': '🇳🇴',
      'Danemark': '🇩🇰', 'Denmark': '🇩🇰',
      'Finlande': '🇫🇮', 'Finland': '🇫🇮',
      'Pologne': '🇵🇱', 'Poland': '🇵🇱',
      'République tchèque': '🇨🇿', 'Czech Republic': '🇨🇿',
      'Hongrie': '🇭🇺', 'Hungary': '🇭🇺',
      'Roumanie': '🇷🇴', 'Romania': '🇷🇴',
      'Bulgarie': '🇧🇬', 'Bulgaria': '🇧🇬',
      'Croatie': '🇭🇷', 'Croatia': '🇭🇷',
      'Slovénie': '🇸🇮', 'Slovenia': '🇸🇮',
      'Slovaquie': '🇸🇰', 'Slovakia': '🇸🇰',
      'Estonie': '🇪🇪', 'Estonia': '🇪🇪',
      'Lettonie': '🇱🇻', 'Latvia': '🇱🇻',
      'Lituanie': '🇱🇹', 'Lithuania': '🇱🇹',
      'Islande': '🇮🇸', 'Iceland': '🇮🇸',
      'Irlande': '🇮🇪', 'Ireland': '🇮🇪',
      'Grèce': '🇬🇷', 'Greece': '🇬🇷',
      'Chypre': '🇨🇾', 'Cyprus': '🇨🇾',
      'Malte': '🇲🇹', 'Malta': '🇲🇹',
      'Luxembourg': '🇱🇺',
      'Monaco': '🇲🇨',
      'Liechtenstein': '🇱🇮',
      'Saint-Marin': '🇸🇲', 'San Marino': '🇸🇲',
      'Vatican': '🇻🇦',
      'Andorre': '🇦🇩', 'Andorra': '🇦🇩',

      // === NORTH AMERICA ===
      'États-Unis': '🇺🇸', 'United States': '🇺🇸', 'USA': '🇺🇸',
      'Canada': '🇨🇦',
      'Mexique': '🇲🇽', 'Mexico': '🇲🇽',

      // === OCEANIA ===
      'Australie': '🇦🇺', 'Australia': '🇦🇺',
      'Nouvelle-Zélande': '🇳🇿', 'New Zealand': '🇳🇿',
      'Fidji': '🇫🇯', 'Fiji': '🇫🇯',

      // === ASIA ===
      'Japon': '🇯🇵', 'Japan': '🇯🇵',
      'Corée du Sud': '🇰🇷', 'South Korea': '🇰🇷',
      'Chine': '🇨🇳', 'China': '🇨🇳',
      'Singapour': '🇸🇬', 'Singapore': '🇸🇬',
      'Hong Kong': '🇭🇰',
      'Taïwan': '🇹🇼', 'Taiwan': '🇹🇼',
      'Malaisie': '🇲🇾', 'Malaysia': '🇲🇾',
      'Thaïlande': '🇹🇭', 'Thailand': '🇹🇭',
      'Indonésie': '🇮🇩', 'Indonesia': '🇮🇩',
      'Philippines': '🇵🇭',
      'Vietnam': '🇻🇳',
      'Inde': '🇮🇳', 'India': '🇮🇳',

      // === AFRICA ===
      'Afrique du Sud': '🇿🇦', 'South Africa': '🇿🇦',
      'Maroc': '🇲🇦', 'Morocco': '🇲🇦',
      'Tunisie': '🇹🇳', 'Tunisia': '🇹🇳',
      'Algérie': '🇩🇿', 'Algeria': '🇩🇿',
      'Égypte': '🇪🇬', 'Egypt': '🇪🇬',
      'Kenya': '🇰🇪',
      'Ghana': '🇬🇭',
      'Nigeria': '🇳🇬',

      // === SOUTH AMERICA ===
      'Brésil': '🇧🇷', 'Brazil': '🇧🇷',
      'Argentine': '🇦🇷', 'Argentina': '🇦🇷',
      'Chili': '🇨🇱', 'Chile': '🇨🇱',
      'Colombie': '🇨🇴', 'Colombia': '🇨🇴',
      'Pérou': '🇵🇪', 'Peru': '🇵🇪',
      'Uruguay': '🇺🇾',
      'Équateur': '🇪🇨', 'Ecuador': '🇪🇨',
      'Venezuela': '🇻🇪',

      // === MIDDLE EAST ===
      'Israël': '🇮🇱', 'Israel': '🇮🇱',
      'Émirats arabes unis': '🇦🇪', 'UAE': '🇦🇪',
      'Qatar': '🇶🇦',
      'Arabie saoudite': '🇸🇦', 'Saudi Arabia': '🇸🇦',
      'Turquie': '🇹🇷', 'Turkey': '🇹🇷',
      'Liban': '🇱🇧', 'Lebanon': '🇱🇧',
      'Jordanie': '🇯🇴', 'Jordan': '🇯🇴',

      // === OTHER ===
      'Russie': '🇷🇺', 'Russia': '🇷🇺',
      'Ukraine': '🇺🇦',
      'Biélorussie': '🇧🇾', 'Belarus': '🇧🇾',
    };
    return flagMap[countryName] || '🌍';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50/30 via-white to-primary-100/20 pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Chargement des pays...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50/30 via-white to-primary-100/20">
      {/* Compact Hero Section - Matching Website Design */}
      <section className="bg-white pt-20 pb-4" style={{ paddingTop: '4rem' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center mb-3">
            <span className="text-3xl mr-3">🌍</span>
            <h1 style={{
              fontSize: '24px',
              marginBottom: '0',
              color: '#2563eb',
              fontWeight: '700',
              textTransform: 'capitalize'
            }}>
              Tous les Pays
            </h1>
          </div>

          <div className="archive-description">
            <p style={{
              marginBottom: '15px',
              textAlign: 'justify',
              color: '#3d3d3d',
              fontSize: '17px',
              lineHeight: '30px',
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif'
            }}>
              Découvrez un monde d'opportunités académiques à travers notre sélection complète de bourses d'études internationales.
              De l'Europe prestigieuse à l'Asie innovante, de l'Amérique du Nord dynamique à l'Océanie accueillante,
              chaque destination offre des programmes d'excellence adaptés à tous les niveaux d'études - Licence, Master et Doctorat.
            </p>
            <p style={{
              marginBottom: '20px',
              textAlign: 'justify',
              color: '#3d3d3d',
              fontSize: '17px',
              lineHeight: '30px',
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif'
            }}>
              Que vous rêviez d'étudier dans les universités centenaires d'Europe, les campus technologiques d'Asie,
              ou les institutions de recherche de renommée mondiale en Amérique, votre parcours académique international commence ici.
              Explorez, comparez et trouvez la bourse qui transformera vos ambitions en réalité.
            </p>
          </div>

          {/* Quick Stats */}
          <div className="flex flex-wrap gap-3 mt-3">
            <div className="flex items-center bg-blue-50 px-3 py-1.5 rounded-lg">
              <svg className="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm font-medium text-blue-800">{countries.length} Pays Disponibles</span>
            </div>
            <div className="flex items-center bg-green-50 px-3 py-1.5 rounded-lg">
              <svg className="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
              <span className="text-sm font-medium text-green-800">Bourses Gratuites</span>
            </div>
            <div className="flex items-center bg-purple-50 px-3 py-1.5 rounded-lg">
              <svg className="w-4 h-4 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span className="text-sm font-medium text-purple-800">Mise à Jour Régulière</span>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Search Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-lg mx-auto">
          <div className="text-center mb-4">
            <h2 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#3d3d3d',
              marginBottom: '8px',
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif'
            }}>
              Trouvez Votre Destination Idéale
            </h2>
          </div>

          <div className="relative group">
            <input
              type="text"
              placeholder="Rechercher un pays... (ex: France, Canada, Allemagne)"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                fontSize: '15px',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif',
                color: '#3d3d3d'
              }}
              className="w-full px-5 py-3 pl-16 pr-12 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-300 shadow-sm hover:shadow-md group-hover:border-gray-300"
            />
            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <svg className="h-4 w-4 text-gray-400 group-hover:text-primary transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>

          {searchTerm && (
            <div className="mt-3 text-center">
              <p style={{
                fontSize: '13px',
                color: '#767676',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif'
              }}>
                {filteredCountries.length} résultat{filteredCountries.length !== 1 ? 's' : ''} trouvé{filteredCountries.length !== 1 ? 's' : ''} pour "{searchTerm}"
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Content Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        {filteredCountries.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">🔍</div>
            <h3 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#3d3d3d',
              marginBottom: '8px',
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif'
            }}>
              Aucun pays trouvé
            </h3>
            <p style={{
              fontSize: '15px',
              color: '#3d3d3d',
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif'
            }}>
              Essayez de modifier votre recherche ou parcourez tous les pays disponibles.
            </p>
          </div>
        ) : (
          <>
            {/* Countries Grid - 5x3 Layout */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 mb-8">
              {displayedCountries.map((country) => (
                <Link
                  key={country.country}
                  to={`/countries/${encodeURIComponent(country.country)}`}
                  className="group bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden border border-gray-200 hover:border-primary/30 p-4"
                >
                  <div className="text-center">
                    <div className="text-3xl mb-3">
                      {getCountryFlag(country.country)}
                    </div>

                    <h3 style={{
                      fontSize: '15px',
                      fontWeight: '600',
                      color: '#3d3d3d',
                      marginBottom: '8px',
                      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif',
                      lineHeight: '1.3'
                    }} className="group-hover:text-primary transition-colors duration-200">
                      {country.country}
                    </h3>

                    <div className="flex items-center justify-center text-primary text-xs font-medium">
                      <span>{translations.countries.viewScholarships}</span>
                      <svg className="ml-1 h-3 w-3 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </Link>
              ))}
            </div>

            {/* GreatYOP Pagination */}
            {pagination.totalPages > 1 && (
              <div className="mt-8">
                <GreatYOPPagination
                  current={pagination.page}
                  total={pagination.total}
                  pageSize={pagination.limit}
                  onChange={handlePageChange}
                  showQuickJumper={false}
                />
              </div>
            )}
          </>
        )}
      </div>

      {/* Page End Suggestions */}
      <PageEndSuggestions
        currentPageType="country"
      />
    </div>
  );
};

export default Countries;
