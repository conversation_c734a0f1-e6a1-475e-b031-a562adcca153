import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';
import PageEndSuggestions from '../components/PageEndSuggestions';

interface Country {
  country: string;
  count: number;
}

const Countries: React.FC = () => {
  const { translations } = useLanguage();
  const [countries, setCountries] = useState<Country[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCountries, setFilteredCountries] = useState<Country[]>([]);

  useEffect(() => {
    fetchCountries();
  }, []);

  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredCountries(countries);
    } else {
      const filtered = countries.filter(country =>
        country.country.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredCountries(filtered);
    }
  }, [searchTerm, countries]);

  const fetchCountries = async () => {
    try {
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/countries`);
      if (response.ok) {
        const data = await response.json();
        setCountries(data.data || []);
      } else {
        console.error('Failed to fetch countries');
      }
    } catch (error) {
      console.error('Error fetching countries:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCountryFlag = (countryName: string): string => {
    const flagMap: { [key: string]: string } = {
      // === EUROPE ===
      'France': '🇫🇷', 'Allemagne': '🇩🇪', 'Germany': '🇩🇪',
      'Royaume-Uni': '🇬🇧', 'United Kingdom': '🇬🇧', 'UK': '🇬🇧',
      'Italie': '🇮🇹', 'Italy': '🇮🇹',
      'Espagne': '🇪🇸', 'Spain': '🇪🇸',
      'Portugal': '🇵🇹',
      'Pays-Bas': '🇳🇱', 'Netherlands': '🇳🇱',
      'Belgique': '🇧🇪', 'Belgium': '🇧🇪',
      'Suisse': '🇨🇭', 'Switzerland': '🇨🇭',
      'Autriche': '🇦🇹', 'Austria': '🇦🇹',
      'Suède': '🇸🇪', 'Sweden': '🇸🇪',
      'Norvège': '🇳🇴', 'Norway': '🇳🇴',
      'Danemark': '🇩🇰', 'Denmark': '🇩🇰',
      'Finlande': '🇫🇮', 'Finland': '🇫🇮',
      'Pologne': '🇵🇱', 'Poland': '🇵🇱',
      'République tchèque': '🇨🇿', 'Czech Republic': '🇨🇿',
      'Hongrie': '🇭🇺', 'Hungary': '🇭🇺',
      'Roumanie': '🇷🇴', 'Romania': '🇷🇴',
      'Bulgarie': '🇧🇬', 'Bulgaria': '🇧🇬',
      'Croatie': '🇭🇷', 'Croatia': '🇭🇷',
      'Slovénie': '🇸🇮', 'Slovenia': '🇸🇮',
      'Slovaquie': '🇸🇰', 'Slovakia': '🇸🇰',
      'Estonie': '🇪🇪', 'Estonia': '🇪🇪',
      'Lettonie': '🇱🇻', 'Latvia': '🇱🇻',
      'Lituanie': '🇱🇹', 'Lithuania': '🇱🇹',
      'Islande': '🇮🇸', 'Iceland': '🇮🇸',
      'Irlande': '🇮🇪', 'Ireland': '🇮🇪',
      'Grèce': '🇬🇷', 'Greece': '🇬🇷',
      'Chypre': '🇨🇾', 'Cyprus': '🇨🇾',
      'Malte': '🇲🇹', 'Malta': '🇲🇹',
      'Luxembourg': '🇱🇺',
      'Monaco': '🇲🇨',
      'Liechtenstein': '🇱🇮',
      'Saint-Marin': '🇸🇲', 'San Marino': '🇸🇲',
      'Vatican': '🇻🇦',
      'Andorre': '🇦🇩', 'Andorra': '🇦🇩',

      // === NORTH AMERICA ===
      'États-Unis': '🇺🇸', 'United States': '🇺🇸', 'USA': '🇺🇸',
      'Canada': '🇨🇦',
      'Mexique': '🇲🇽', 'Mexico': '🇲🇽',

      // === OCEANIA ===
      'Australie': '🇦🇺', 'Australia': '🇦🇺',
      'Nouvelle-Zélande': '🇳🇿', 'New Zealand': '🇳🇿',
      'Fidji': '🇫🇯', 'Fiji': '🇫🇯',

      // === ASIA ===
      'Japon': '🇯🇵', 'Japan': '🇯🇵',
      'Corée du Sud': '🇰🇷', 'South Korea': '🇰🇷',
      'Chine': '🇨🇳', 'China': '🇨🇳',
      'Singapour': '🇸🇬', 'Singapore': '🇸🇬',
      'Hong Kong': '🇭🇰',
      'Taïwan': '🇹🇼', 'Taiwan': '🇹🇼',
      'Malaisie': '🇲🇾', 'Malaysia': '🇲🇾',
      'Thaïlande': '🇹🇭', 'Thailand': '🇹🇭',
      'Indonésie': '🇮🇩', 'Indonesia': '🇮🇩',
      'Philippines': '🇵🇭',
      'Vietnam': '🇻🇳',
      'Inde': '🇮🇳', 'India': '🇮🇳',

      // === AFRICA ===
      'Afrique du Sud': '🇿🇦', 'South Africa': '🇿🇦',
      'Maroc': '🇲🇦', 'Morocco': '🇲🇦',
      'Tunisie': '🇹🇳', 'Tunisia': '🇹🇳',
      'Algérie': '🇩🇿', 'Algeria': '🇩🇿',
      'Égypte': '🇪🇬', 'Egypt': '🇪🇬',
      'Kenya': '🇰🇪',
      'Ghana': '🇬🇭',
      'Nigeria': '🇳🇬',

      // === SOUTH AMERICA ===
      'Brésil': '🇧🇷', 'Brazil': '🇧🇷',
      'Argentine': '🇦🇷', 'Argentina': '🇦🇷',
      'Chili': '🇨🇱', 'Chile': '🇨🇱',
      'Colombie': '🇨🇴', 'Colombia': '🇨🇴',
      'Pérou': '🇵🇪', 'Peru': '🇵🇪',
      'Uruguay': '🇺🇾',
      'Équateur': '🇪🇨', 'Ecuador': '🇪🇨',
      'Venezuela': '🇻🇪',

      // === MIDDLE EAST ===
      'Israël': '🇮🇱', 'Israel': '🇮🇱',
      'Émirats arabes unis': '🇦🇪', 'UAE': '🇦🇪',
      'Qatar': '🇶🇦',
      'Arabie saoudite': '🇸🇦', 'Saudi Arabia': '🇸🇦',
      'Turquie': '🇹🇷', 'Turkey': '🇹🇷',
      'Liban': '🇱🇧', 'Lebanon': '🇱🇧',
      'Jordanie': '🇯🇴', 'Jordan': '🇯🇴',

      // === OTHER ===
      'Russie': '🇷🇺', 'Russia': '🇷🇺',
      'Ukraine': '🇺🇦',
      'Biélorussie': '🇧🇾', 'Belarus': '🇧🇾',
    };
    return flagMap[countryName] || '🌍';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50/30 via-white to-primary-100/20 pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Chargement des pays...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50/30 via-white to-primary-100/20">
      {/* Enhanced Hero Section - Like Other Pages */}
      <section className="relative overflow-hidden bg-gradient-to-br from-gray-900 via-primary-dark to-primary">
        {/* Background Image with Overlay */}
        <div className="absolute inset-0 z-0">
          <img
            src="https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1200&q=80"
            alt="Students studying worldwide"
            className="w-full h-full object-cover"
            style={{ opacity: 0.3 }}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = 'https://images.unsplash.com/photo-1517486808906-6ca8b3f8e1c1?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1200&q=80';
            }}
          />
          <div
            className="absolute inset-0 mix-blend-multiply"
            style={{
              background: 'linear-gradient(to right, rgba(37, 99, 235, 0.4), rgba(29, 78, 216, 0.4))'
            }}
          />
        </div>

        {/* Hero Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-24 pb-16">
          <div className="text-left">
            <div className="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-white text-sm font-medium mb-6">
              <span className="text-2xl mr-2">🌍</span>
              {countries.length} pays disponibles
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight mb-6">
              Bourses d'Études par Pays
            </h1>

            <div className="max-w-4xl">
              <p className="text-lg md:text-xl text-white/90 mb-6 leading-relaxed">
                Explorez les opportunités de bourses d'études disponibles dans le monde entier.
                Chaque pays offre des programmes uniques adaptés à différents niveaux d'études et domaines de spécialisation.
              </p>

              <p className="text-base md:text-lg text-white/80 leading-relaxed">
                De l'Europe à l'Asie, de l'Amérique du Nord à l'Océanie, découvrez des destinations d'études
                exceptionnelles qui vous permettront de réaliser vos ambitions académiques tout en vivant
                une expérience culturelle enrichissante.
              </p>
            </div>

            <div className="mt-8 flex flex-col sm:flex-row gap-4">
              <div className="px-6 py-3 bg-white text-primary font-medium rounded-lg shadow-lg flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Explorez par Pays
              </div>

              <Link
                to="/scholarships"
                className="px-6 py-3 bg-transparent border border-white text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-300 flex items-center"
              >
                Toutes les Bourses
                <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Search Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-md mx-auto">
          <div className="relative">
            <input
              type="text"
              placeholder={translations.countries.searchPlaceholder}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        {filteredCountries.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Aucun pays trouvé
            </h3>
            <p className="text-gray-600">
              Essayez de modifier votre recherche ou parcourez tous les pays disponibles.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredCountries.map((country) => (
              <Link
                key={country.country}
                to={`/countries/${encodeURIComponent(country.country)}`}
                className="group bg-white rounded-xl shadow-md hover:shadow-xl hover:shadow-primary/10 transition-all duration-300 overflow-hidden border border-gray-100 hover:border-primary/30"
              >
                <div className="p-6">
                  <div className="flex items-center justify-center mb-4">
                    <div className="text-5xl">
                      {getCountryFlag(country.country)}
                    </div>
                  </div>

                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-primary transition-colors duration-200 mb-4 text-center">
                    {country.country}
                  </h3>

                  <div className="flex items-center justify-center text-primary text-sm font-medium">
                    <span>{translations.countries.viewScholarships}</span>
                    <svg className="ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>

      {/* Page End Suggestions */}
      <PageEndSuggestions
        currentPageType="country"
      />
    </div>
  );
};

export default Countries;
